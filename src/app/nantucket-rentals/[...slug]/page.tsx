import ListingHeroImages from '@/app/components/rental-listing-details/ListingHeroImages';
import PropertyDetails from '@/app/components/rental-listing-details/PropertyDetails';
import PropertyFeaturedAmenities from '@/app/components/rental-listing-details/PropertyFeaturedAmenities';
import PropertyHouseRules from '@/app/components/rental-listing-details/PropertyHouseRules';
import PropertySleepingArrangement from '@/app/components/rental-listing-details/PropertySleepingArrangement';
import { generatePropertyRentaltJsonLd } from '@/app/components/rental-listing/PropertyCard';
import { getListingDetailsByNeighborhoodAndSlug } from '@/app/services/rental-listing-details';
import { H1, H2 } from '@/app/ui/Typography';
import { Separator } from '@/app/ui/separator';
import ExpandableText from '@/clients/components/common/ExpandableText';
import PropertyCheckout from '@/clients/components/rental-listing-details/PropertyCheckout';
import StickyBookButton from '@/clients/components/rental-listing-details/StickyBookButton';
import RentalDetailsContextContainer from '@/clients/contexts/RentalDetailsContext';
import { RequestToBookContextProvider } from '@/clients/contexts/RequestToBookContext';
import { IListingDetails } from '@/types/rental-listing-details';
import { isMobileDevice } from '@/utils/responsive';
import { GoogleMapsEmbed } from '@next/third-parties/google';

import { Metadata, ResolvingMetadata } from 'next';
import { headers } from 'next/headers';
import Image from 'next/image';
import { redirect } from 'next/navigation';
import Script from 'next/script';

export const revalidate = 600;

const MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '';

type PageProps = {
  params: { slug: string | string[] };
};

export async function generateMetadata(
  { params }: PageProps,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const formattedSlug = (params.slug as string[]).join('/') ?? '';

  const listing = await getListingDetailsByNeighborhoodAndSlug<IListingDetails>(formattedSlug);

  const previousImages = (await parent)?.openGraph?.images || [];

  // Handle case when listing is null
  if (!listing) {
    return {
      title: 'Property Not Found | Congdon & Coleman',
      description: 'The requested property could not be found.',
      metadataBase: new URL('https://www.congdonandcoleman.com'),
      robots: 'noindex,nofollow',
      openGraph: {
        images: [...previousImages],
      },
    };
  }

  return {
    title: listing.title_tag ?? '',
    description: listing.meta_description ?? '',
    metadataBase: new URL('https://www.congdonandcoleman.com'),
    alternates: {
      canonical: 'https://www.congdonandcoleman.com/nantucket-rentals/',
    },
    robots: listing.meta_robots ?? 'noindex,nofollow',
    openGraph: {
      images: [...previousImages],
    },
  };
}

export default async function RentalListingDetails({ params }: PageProps) {
  let isMobile = false;
  try {
    const userAgent = headers().get('user-agent');
    isMobile = isMobileDevice(userAgent ?? '');
  } catch (error) {
    console.error('Error determining device type:', error);
    // Default to desktop view if there's an error
  }
  const formattedSlug = (params.slug as string[]).join('/') ?? '';

  const rentalListingDetails =
    await getListingDetailsByNeighborhoodAndSlug<IListingDetails>(formattedSlug);

  if (!rentalListingDetails) {
    redirect('/nantucket-rentals/');
  }

  const propertyTitle = `${rentalListingDetails.address} | ${rentalListingDetails.area.name}`;

  return (
    <main className=''>
      <ListingHeroImages
        listingImages={rentalListingDetails?.images ?? []}
        propertyHeadline={propertyTitle}
        isMobile={isMobile}
      />
      <div className='container xl:p-0'>
        <RequestToBookContextProvider
          nrPropertyId={rentalListingDetails.listing_id}
          property={rentalListingDetails}
        >
          <RentalDetailsContextContainer listingDetails={rentalListingDetails}>
            <div className='flex gap-x-6 pt-4 pb-[130px] md:pb-20'>
              <div className='w-full md:flex-grow md:w-[calc(100%-480px)]'>
                <H1 className='font-medium m-0 leading-normal mb-2'>{propertyTitle}</H1>
                <Separator className='my-2 md:my-3' />
                <div className='flex items-center gap-x-2'>
                  <p className='m-0 text-xs md:text-sm'>Hosted by</p>
                  <Image width={34} height={38} alt='CNC logo' src='/images/cnc-logo.png' />
                  <div>
                    <p className='m-0 text-xs font-medium'>Congdon & Coleman Real Estate</p>
                    <p className='m-0 text-[10px]'>Serving Nantucket Since 1931</p>
                  </div>
                </div>

                <Separator className='my-2 md:my-3' />
                <PropertyDetails details={rentalListingDetails} />
                <PropertySleepingArrangement bedrooms={rentalListingDetails.bedrooms} />
                <PropertyFeaturedAmenities details={rentalListingDetails} />
                <Separator className='my-4 md:my-8' />
                <ExpandableText
                  className='text-sm md:text-base text-metal-gray m-0'
                  text={rentalListingDetails.description}
                />
                <Separator className='my-4 md:my-8' />
                <H2 className='m-0 mb-4 md:mb-6 flex items-center gap-x-2'>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    width='36'
                    height='37'
                    viewBox='0 0 36 37'
                    fill='none'
                  >
                    <path
                      fillRule='evenodd'
                      clipRule='evenodd'
                      d='M6.375 13.2207C6.375 7.12769 11.6572 2.32373 18 2.32373C24.3428 2.32373 29.625 7.12769 29.625 13.2207C29.625 18.9374 26.0807 25.6488 20.3622 28.0926C18.8609 28.7341 17.1391 28.7341 15.6378 28.0926C9.91933 25.6488 6.375 18.9374 6.375 13.2207ZM18 4.57373C12.7448 4.57373 8.625 8.51987 8.625 13.2207C8.625 18.2132 11.7831 23.9984 16.522 26.0236C17.4585 26.4238 18.5415 26.4238 19.478 26.0236C24.2169 23.9984 27.375 18.2132 27.375 13.2207C27.375 8.51987 23.2552 4.57373 18 4.57373ZM18 12.0737C16.9645 12.0737 16.125 12.9132 16.125 13.9487C16.125 14.9843 16.9645 15.8237 18 15.8237C19.0355 15.8237 19.875 14.9843 19.875 13.9487C19.875 12.9132 19.0355 12.0737 18 12.0737ZM13.875 13.9487C13.875 11.6706 15.7218 9.82373 18 9.82373C20.2782 9.82373 22.125 11.6706 22.125 13.9487C22.125 16.2269 20.2782 18.0737 18 18.0737C15.7218 18.0737 13.875 16.2269 13.875 13.9487ZM5.39312 22.9436C5.81016 23.4042 5.77488 24.1156 5.31432 24.5326C4.46278 25.3037 4.125 26.0418 4.125 26.6987C4.125 27.8442 5.21101 29.256 7.85506 30.4458C10.3935 31.5881 13.9793 32.3237 18 32.3237C22.0207 32.3237 25.6065 31.5881 28.1449 30.4458C30.789 29.256 31.875 27.8442 31.875 26.6987C31.875 26.0418 31.5372 25.3037 30.6857 24.5326C30.2251 24.1156 30.1898 23.4042 30.6069 22.9436C31.0239 22.4831 31.7354 22.4478 32.1959 22.8648C33.3404 23.9012 34.125 25.2004 34.125 26.6987C34.125 29.2811 31.8531 31.2444 29.0683 32.4976C26.1778 33.7983 22.2635 34.5737 18 34.5737C13.7365 34.5737 9.82223 33.7983 6.93174 32.4976C4.14685 31.2444 1.875 29.2811 1.875 26.6987C1.875 25.2004 2.65957 23.9012 3.80408 22.8648C4.26464 22.4478 4.97608 22.4831 5.39312 22.9436Z'
                      fill='#1184B7'
                    />
                  </svg>
                  Explore the Neighborhood
                </H2>

                <div className='rounded-lg overflow-hidden'>
                  <GoogleMapsEmbed
                    apiKey={MAPS_API_KEY}
                    mode='place'
                    zoom='16'
                    height={340}
                    width='100%'
                    q={`${rentalListingDetails.latitude ?? 41.2835},${
                      rentalListingDetails.longitude ?? -70.0995
                    }`}
                  />
                </div>
                <Separator className='my-4 md:my-8' />
                <PropertyHouseRules details={rentalListingDetails} />
              </div>
              <div className='hidden md:block w-full md:w-[480px]'>
                <PropertyCheckout
                  details={rentalListingDetails}
                  isMobile={isMobile}
                  petsAllowed={
                    rentalListingDetails?.requirement?.pet_allow?.toLowerCase() === 'true'
                  }
                />
              </div>
            </div>
            <StickyBookButton
              details={rentalListingDetails}
              petsAllowed={rentalListingDetails?.requirement?.pet_allow?.toLowerCase() === 'true'}
            />
          </RentalDetailsContextContainer>
        </RequestToBookContextProvider>
      </div>
      <Script
        id={`rental-${rentalListingDetails.slug.split(' ').join().toLocaleLowerCase()}`}
        type='application/ld+json'
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generatePropertyRentaltJsonLd(rentalListingDetails)),
        }}
      />
    </main>
  );
}
