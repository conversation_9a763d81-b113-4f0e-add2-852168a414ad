import { Nullable } from './common';

export type IListingBathroom = {
  bathroom_uuid: string;
  bidet?: boolean;
  toilet?: boolean;
  tub?: boolean;
  combination_tub?: boolean;
  jetted_tub?: boolean;
  shower?: boolean;
  outdoor_shower?: boolean;
  bedroom?: any;
  type: {
    name: string;
  };
};

export type IBedType = {
  bed_uuid: string;
  number: number;
  type: ListingItemWithId;
};

export type ListingItemWithId = {
  id: number;
  name: string;
};

export type IListingBedroom = {
  bedroom_uuid: string;
  beds: IBedType[];
  en_suite?: boolean;
  floor_level: {
    name?: string;
  };
};

export type IListingImage = {
  image_uuid: string;
  url: string;
  small_url: string;
  alt_text?: string;
  order: number;
  tags?: string[];
  object: string;
};

export type IListingRate = {
  rate_uuid: string;
  from_date: string;
  to_date: string;
  amount: string;
  weekly_amount: string;
  is_nightly_rate: boolean;
  weekly_discount: Nullable<number>;
  monthly_discount: Nullable<number>;
  minimum_booking_amount: Nullable<number>;
  minimum_nights_stay: Nullable<number>;
  allow_checkin: boolean;
  allow_checkout: boolean;
  early_bird_discount: Nullable<number>;
  early_bird_discount_months: Nullable<number>;
  custom_discount: Nullable<number>;
  custom_discount_days: Nullable<number>;
  apply_last_minute_rule: Nullable<boolean>;
  last_minute_rule_days: Nullable<number>;
  last_minute_discount: Nullable<number>;
  last_minute_minimum_nights_stay: Nullable<number>;
  last_minute_allow_checkin: Nullable<boolean>;
  last_minute_allow_checkout: Nullable<boolean>;
  last_minute_minimum_booking_amount: Nullable<number>;
};

export type IListingAvailability = {
  from_date: string;
  to_date: string;
  availability?: boolean;
  type: ListingAvailabilityType;
  availability_uuid: string;
  rent_amount: number;
  custom_agent: Nullable<string>;
  custom_source: Nullable<string>;
  renter_name: Nullable<string>;
  agreement_url: string;
  lease: Nullable<string>;
};

export enum ListingAvailabilityType {
  OWNER_TIME = 'owner',
  LEASED = 'leased',
  OTHER = 'other',
}

export type IListingRequirement = {
  pet_allow_label: string;
  min_night_stay: number;
  cleaning_fee?: number | string;
  cleaning_hours: number;
  pet_allow?: string;
  pet_fee?: string;
  security_deposit_percentage?: number;
  min_security_deposit?: null;
  commission_percentage?: number;
  payment_number?: number;
  turnover_day: string;
  checkin_time: string;
  checkout_time: string;
};

export interface IListingDetails {
  listing_id: number;
  bathrooms: IListingBathroom[];
  bedrooms: IListingBedroom[];
  images: IListingImage[];
  rates: IListingRate[];
  availabilities: IListingAvailability[];
  requirement: IListingRequirement;
  homeowner: ListingItemWithId;
  peak_rate: number;
  shoulder_rate: number;
  created_at: string;
  updated_at: string;
  old_listing_id: number;
  key_number: string;
  publish?: boolean;
  avg_rating?: number;
  ratings_count?: number;
  other_rental_firms?: any[];
  latest_leased_info?: {
    lease_id: number;
    leased_at?: string;
    leased_by: string;
  };
  area_name: string;
  max_price: number;
  min_price: number;
  url: string;
  small_url: string;
  house_phone?: string;
  guidebook_link?: string;
  virtual_tour_link?: string;
  occupancy_tax_number?: string;
  address: string;
  zip_code: string;
  headline: string;
  description: string;
  short_description?: string;
  first_floor?: string;
  second_floor?: string;
  third_floor?: string;
  lower_level?: string;
  other_structure?: string;
  bedroom_number: number;
  bathroom_number: number;
  half_bathroom_number?: number;
  capacity: number;
  air_conditioning?: boolean;
  ac_types: ListingItemWithId[];
  clothes_dryer: boolean;
  fan: false;
  fan_quantity: number;
  fireplace: boolean;
  fireplace_quantity: number;
  gym: false;
  hair_dryer: false;
  heating: boolean;
  iron: boolean;
  ironing_board: boolean;
  jacuzzi: false;
  linen: boolean;
  parking: boolean;
  parking_quantity: number;
  safe: false;
  towel: boolean;
  washing_machine: boolean;
  wifi: boolean;
  wifi_network_name: 'acklast';
  wifi_password: 'rochelle';
  disposal: false;
  counter_seating: boolean;
  dining_area: boolean;
  dining_room: false;
  outdoor_dining_area: false;
  seating: number;
  dining_description: null;
  bluetooth_speaker: false;
  book: false;
  dvd_player: boolean;
  tv: boolean;
  tv_quantity: number;
  bbq_tool: false;
  blender: boolean;
  coffee_maker: boolean;
  dish_or_cup_or_utensil: boolean;
  dishwasher: boolean;
  food_processor: boolean;
  ice_maker?: boolean;
  ice_tray?: boolean;
  lobster_pot: boolean;
  lobster_utensil?: boolean;
  microwave?: boolean;
  oven: boolean;
  pot_or_pan: boolean;
  refrigerator: boolean;
  stove: boolean;
  tea_kettle?: boolean;
  toaster: boolean;
  beach_chair: boolean;
  beach_chair_quantity: number;
  beach_towel: boolean;
  beach_towel_quantity: number;
  beach_umbrella: boolean;
  bicycle?: boolean;
  bicycle_quantity: number;
  cooler?: boolean;
  deck: boolean;
  grill: boolean;
  hot_tub_or_spa?: boolean;
  lawn_or_garden: boolean;
  outdoor_furniture: boolean;
  outdoor_shower: boolean;
  patio: boolean;
  pool: boolean;
  porch?: boolean;
  yard?: boolean;
  tennis_court?: boolean;
  distance_to_beach?: string;
  distance_to_the_hub?: string;
  year_built: number;
  year_renovated?: string | number;
  waterfront?: boolean;
  water_view: false;
  walk_to_beach: false;
  calendar_update_email: boolean;
  title_tag: string;
  meta_description: string;
  meta_robots: string;
  meta_canonical: string;
  slug: string;
  user: number;
  share_listing_with?: any;
  priority: ListingItemWithId;
  occupancy_tax_file?: any;
  area: ListingItemWithId;
  heating_type: ListingItemWithId;
  tv_service: ListingItemWithId;
  coffee_maker_type?: any;
  stove_type?: any;
  toaster_type?: any;
  grill_type: ListingItemWithId;
  pool_type: ListingItemWithId;
  tennis_court_type?: any;
  featured_amenities?: string[];
  longitude?: number;
  latitude?: number;
}

export type Rent = {
  rent: number;
  fee: number;
  tax: number;
  adjusted_checkin_date: string;
  adjusted_checkout_date: string;
  discount: number;
  discount_type: string;
};
