'use client';

import { ReactNode, useEffect } from 'react';

import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import localeData from 'dayjs/plugin/localeData';
import { AppProgressBar as ProgressBar } from 'next-nprogress-bar';
import { usePathname, useSearchParams } from 'next/navigation';

dayjs.extend(localeData);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(isBetween);
dayjs().localeData();

type Props = {
  children: ReactNode;
};

const GlobalProviders = ({ children }: Props) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    window.analytics.page({
      path: pathname,
      url: document.URL,
      search: searchParams.toString(),
      referrer: document.referrer,
    });
  }, [pathname, searchParams]);

  useEffect(() => {
    if ((document?.getElementById('cnc_mobile_menu_drawer') as any)?.checked) {
      document?.getElementById('cnc_mobile_menu_drawer')?.click();
    }
  }, [pathname]);

  return (
    <>
      {children}
      <ProgressBar color='#d9eaf0' options={{ showSpinner: false }} shallowRouting />
    </>
  );
};

export default GlobalProviders;

declare global {
  interface Window {
    analytics: {
      page: any;
      track: any;
      reset: any;
    };
  }
}
