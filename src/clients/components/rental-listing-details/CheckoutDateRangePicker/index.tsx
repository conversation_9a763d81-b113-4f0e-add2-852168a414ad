'use client';

import throttle from 'lodash/throttle';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { DateRange, DayContentProps, SelectRangeEventHandler } from 'react-day-picker';

import Button from '@/clients/ui/button';
import { Calendar } from '@/clients/ui/calendar';
import { Skeleton } from '@/clients/ui/skeleton';
import { cn } from '@/lib/utils';
// import { AvailabilityCalendarData, RentalRatesCalendarData } from '@/types/properties';
import { IListingAvailability, IListingRate, Rent } from '@/types/rental-listing-details';
// import { formatBookingFeesData } from '@/utils/booking';
import {
  currencyFormatter,
  currencyFormatterRound,
  Nullable,
  parseDateString,
} from '@/utils/common';
import {
  checkIfDateCannotBeCheckedIn,
  formatDateRangePickerRentalRates,
  getBlockedRangesForDate,
  getBlockedStartAndEndDates,
  getCheckinDayRelativeToADate,
  isForcedCheckinDay,
  isDateRangeSelected,
  getStartAndEndForcedCheckinDay,
  checkIfDateDisabledDesktop,
} from '@/utils/date-range-picker';

import {
  addYears,
  differenceInCalendarDays,
  endOfMonth,
  format,
  isBefore,
  isSameDay,
  isValid,
  startOfDay,
  startOfMonth,
  subMonths,
} from 'date-fns';

import RDPCustomDate from './RDPCustomDate';

type Props = {
  onClear: () => void;
  onClose: () => void;
  rentalRates?: IListingRate[];
  availableCalendar?: IListingAvailability[];
  propertyId: number;
  date?: DateRange;
  setDate: (_d?: DateRange) => void;
  rentInfo?: Nullable<Rent>;
  isFetchingBookingDetails?: boolean;
  showSave?: boolean;
  onSave?: () => void;
};

const CheckoutDateRangePicker = ({
  onClear,
  onClose,
  availableCalendar = [],
  rentalRates: rentalRatesDefault = [],
  propertyId,
  date,
  setDate,
  rentInfo,
  isFetchingBookingDetails,
  showSave,
  onSave,
}: Props) => {
  // Calculate number of nights and booking data
  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date?.to, date?.from) : 0),
    [date?.from, date?.to],
  );

  // const bookingData = useMemo(
  //   () =>
  //     rentInfo && formatBookingFeesData(rentInfo, '', numberOfNights),
  //   [rentInfo, numberOfNights],
  // );

  // Date range configuration
  const maxSelectableDate = useMemo(() => endOfMonth(subMonths(addYears(new Date(), 2), 1)), []);

  // // Fetch rental rates data
  // const { data } = usePropertyRentalRates(
  //   propertyId,
  //   format(startOfMonth(new Date()), 'yyyy-MM-dd'),
  //   format(maxSelectableDate, 'yyyy-MM-dd'),
  // );

  // Format rental rates data
  const rentalRatesMap = useMemo(() => {
    const rentalRatesSource = rentalRatesDefault;
    return formatDateRangePickerRentalRates(rentalRatesSource);
  }, [rentalRatesDefault]);

  // Popup info state
  const [popUpInfo, setPopUpInfo] = useState<
    Nullable<{
      dt: Date;
      text: string;
    }>
  >(null);

  // Clear dates handler
  const onClearDates = useCallback(() => {
    onClear();
    setPopUpInfo(null);
  }, [onClear]);

  // Get blocked dates
  const { start: blockedStartDates, end: blockedEndDates } = useMemo(
    () =>
      getBlockedStartAndEndDates(
        availableCalendar.filter(
          (_a) =>
            !isBefore(
              startOfDay(parseDateString(_a.from_date)),
              startOfDay(subMonths(new Date(), 1)),
            ) &&
            !isBefore(
              startOfDay(parseDateString(_a.to_date)),
              startOfDay(subMonths(new Date(), 1)),
            ),
        ),
      ),
    [availableCalendar],
  );

  // Date selection status
  const isCheckinSelected = useMemo(() => date?.from && isValid(date.from), [date?.from]);
  const isSelectedValid = useMemo(() => isDateRangeSelected(date), [date]);

  // Get rental data for start date
  const rentalDataForStartDate = useMemo(
    () =>
      date?.from &&
      (rentalRatesMap[format(date?.from, 'MMM-yyyy')] ?? []).find(
        (_r) => date?.from && isSameDay(parseDateString(_r.from_date), date?.from),
      ),
    [date?.from, rentalRatesMap],
  );

  // Header text based on selection state
  const headerText = useMemo(() => {
    if (isCheckinSelected) {
      if (isSelectedValid && date?.from && date?.to) {
        return `${differenceInCalendarDays(date?.to, date?.from)} nights`;
      }
      return 'Select check-out date';
    } else {
      return 'Select check-in date';
    }
  }, [isCheckinSelected, isSelectedValid, date?.from, date?.to]);

  // Mouse over date handler
  const throttledOnMouseOverDate = useMemo(
    () =>
      throttle((_dt: Date) => {
        const rentalRatesForDt = (rentalRatesMap[format(_dt, 'MMM-yyyy')] ?? []).find((_r) =>
          isSameDay(parseDateString(_r.from_date), _dt),
        );

        if (rentalRatesForDt?.allow_checkin && !date?.from) {
          return;
        }

        const checkinDay = getCheckinDayRelativeToADate(
          rentalRatesMap[format(_dt, 'MMM-yyyy')] ?? [],
          _dt,
          availableCalendar,
        );

        if (checkinDay) {
          setPopUpInfo({ dt: _dt, text: `From ${checkinDay} to ${checkinDay}` });
          return;
        }

        const isCheckInRestricted = checkIfDateCannotBeCheckedIn(
          _dt,
          checkinDay,
          rentalRatesForDt,
          rentalDataForStartDate,
        );

        if (
          (date?.from && !date?.to && isCheckInRestricted) ||
          (date?.from && isSameDay(date.from, _dt))
        ) {
          setPopUpInfo({
            dt: _dt,
            text: `${rentalDataForStartDate?.minimum_nights_stay}-night minimum`,
          });
        }
      }, 100),
    [availableCalendar, date?.from, date?.to, rentalDataForStartDate, rentalRatesMap],
  );

  // Mouse leave date handler
  const throttledOnMouseLeaveDate = useMemo(() => throttle(() => setPopUpInfo(null), 100), []);

  // Check if date is selectable
  const isDateSelectable = useCallback(
    (dt: Date) => {
      const monthRates = rentalRatesMap[format(dt, 'MMM-yyyy')] ?? [];
      const rentalRate = monthRates.find((_r) => isSameDay(parseDateString(_r.from_date), dt));

      if (!rentalRate) return false;

      const checkinDay = getCheckinDayRelativeToADate(monthRates, dt, availableCalendar);
      const isCheckInRestricted = checkIfDateCannotBeCheckedIn(
        dt,
        checkinDay,
        rentalRate,
        rentalDataForStartDate,
      );

      if (
        (!date?.from && !rentalRate.allow_checkin) ||
        (date?.from && !rentalRate.allow_checkout) ||
        isCheckInRestricted
      ) {
        return false;
      }

      return true;
    },
    [availableCalendar, date?.from, rentalDataForStartDate, rentalRatesMap],
  );

  // Date selection handler
  const handleDateSelect = useCallback(
    (range: DateRange, dt: Date) => {
      if (!isDateSelectable(dt)) return;
      setDate(range);
    },
    [isDateSelectable, setDate],
  );

  // Calendar disabled dates
  const calendarDisabledDates = useMemo(
    () => [
      { before: new Date(), after: maxSelectableDate },
      (dt: Date) => {
        const blockedRanges = getBlockedRangesForDate(availableCalendar, dt);
        const checkinDay = getCheckinDayRelativeToADate(
          rentalRatesMap[format(dt, 'MMM-yyyy')] ?? [],
          dt,
          availableCalendar,
        );
        const rentalRate = (rentalRatesMap[format(dt, 'MMM-yyyy')] ?? []).find((_r) =>
          isSameDay(parseDateString(_r.from_date), dt),
        );
        const [firstCheckinDay, lastCheckinDay] = getStartAndEndForcedCheckinDay(
          rentalRatesMap[format(dt, 'MMM-yyyy')] ?? [],
        );

        const isDisabled =
          checkIfDateDisabledDesktop(
            checkinDay,
            firstCheckinDay,
            lastCheckinDay,
            date,
            rentalRate,
            availableCalendar,
          ) ||
          (blockedRanges.length > 0 &&
            !date?.from &&
            !blockedStartDates.some((_d) => isSameDay(startOfDay(dt), startOfDay(_d))) &&
            !blockedEndDates.some((_d) => isSameDay(startOfDay(dt), startOfDay(_d))));

        return isDisabled;
      },
    ],
    [
      availableCalendar,
      blockedStartDates,
      blockedEndDates,
      date,
      rentalRatesMap,
      maxSelectableDate,
    ],
  );

  // Render day content
  const renderDayContent = useCallback(
    (props: DayContentProps) => (
      <RDPCustomDate
        {...props}
        selected={date}
        rentalRateForDate={(rentalRatesMap[format(props.date, 'MMM-yyyy')] ?? []).find((_r) =>
          isSameDay(parseDateString(_r.from_date), props.date),
        )}
        blockedStartDates={blockedStartDates}
        blockedEndDates={blockedEndDates}
        popUpInfo={popUpInfo}
        checkinDay={getCheckinDayRelativeToADate(
          rentalRatesMap[format(props.date, 'MMM-yyyy')] ?? [],
          props.date,
          availableCalendar,
        )}
        isForcedCheckinDay={props.date.getDay() === 6}
      />
    ),
    [availableCalendar, blockedEndDates, blockedStartDates, date, popUpInfo, rentalRatesMap],
  );

  // Cleanup throttled functions
  useEffect(() => {
    return () => {
      throttledOnMouseOverDate.cancel();
      throttledOnMouseLeaveDate.cancel();
    };
  }, [throttledOnMouseLeaveDate, throttledOnMouseOverDate]);

  return (
    <>
      <div className='flex items-center justify-between pb-4'>
        <div className='w-1/2 pl-4'>
          <p className='m-0 text-xl font-medium'>{headerText}</p>
          <p className='text-xs text-[#6D7380] m-0 mt-2'>
            {isSelectedValid && date?.from && date?.to
              ? `${format(date?.from, 'LLL d, yyyy')} - ${format(date?.to, 'LLL d, yyyy')}`
              : `Add your travel dates for exact pricing`}
          </p>
        </div>
      </div>

      <Calendar
        initialFocus
        fromMonth={new Date()}
        toMonth={maxSelectableDate}
        onAdditionalDayMouseEnter={throttledOnMouseOverDate}
        onDayMouseLeave={throttledOnMouseLeaveDate}
        components={{ DayContent: renderDayContent }}
        classNames={{
          day_range_start: 'day-range-start !text-white !bg-olive !border-transparent ',
          day_range_end: 'day-range-end !text-white !bg-olive !border-transparent',
          day: cn(
            // buttonVariants({ variant: 'ghost' }),
            'relative cursor-pointer h-10 w-10 p-0 aria-selected:opacity-100 rounded-full border border-transparent border-solid hover:border-[#6D7380] hover:rounded-full [&:has(>.no-checkin)]:cursor-default [&:has(>.blockedBoundary)]:text-[#757575]',
          ),
        }}
        disabled={calendarDisabledDates}
        mode='range'
        defaultMonth={date?.from}
        selected={date}
        onSelect={handleDateSelect as SelectRangeEventHandler}
        numberOfMonths={2}
        className='p-0'
      />
      <div className='flex items-center justify-between gap-x-4 mt-2 h-[50px]'>
        <div>
          {rentInfo && isDateRangeSelected(date) ? (
            <>
              <span className='font-medium text-[24px]'>
                {currencyFormatter.format((rentInfo?.rent ?? 0) / numberOfNights)}
              </span>{' '}
              <span className='text-xs'>Per Night</span>
            </>
          ) : isFetchingBookingDetails ? (
            <>
              <Skeleton className='w-[120px] h-6 mb-2.5' />
            </>
          ) : (
            <p className='m-0'>Add date for price</p>
          )}
        </div>
        <div className='flex items-center'>
          <Button
            intent='ghost'
            className='border-solid text-sm font-normal underline mr-4'
            onClick={onClearDates}
          >
            Clear dates
          </Button>
          {showSave ? (
            <Button
              className='font-medium rounded-[32px] w-[120px]'
              disabled={!isSelectedValid}
              onClick={onSave}
            >
              Save
            </Button>
          ) : (
            <Button
              intent='outline'
              className='text-sm font-normal rounded-[32px]'
              onClick={onClose}
            >
              Close
            </Button>
          )}
        </div>
      </div>
    </>
  );
};

export default CheckoutDateRangePicker;
