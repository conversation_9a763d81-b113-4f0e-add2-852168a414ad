'use client';

import { memo, MouseEvent, useCallback, useMemo } from 'react';

import { DateRange } from 'react-day-picker';

import { IListingAvailability, IListingRate } from '@/types/rental-listing-details';
import {
  checkIfDateCannotBeCheckedIn,
  checkIfDateDisabledDesktop,
  checkIfDateHasRate,
  getBlockedRangesForDate,
  getCheckinDayRelativeToADate,
  getStartAndEndForcedCheckinDay,
  isBetweenTwoDates,
  isForcedCheckinDay,
} from '@/utils/date-range-picker';

import classNames from 'classnames';
import { format, isAfter, isBefore, isSameDay, isValid, startOfDay } from 'date-fns';

import { DatePickerInfoPopUp } from './CheckoutDateRangePickerMobile';

type Props = {
  dateObject: Date;
  date: number;
  active?: boolean;
  insideCurrentMonth: boolean;
  selected?: DateRange;
  onSelectDate: (_d?: DateRange) => void;
  availabilityData: IListingAvailability[];
  availabilityDataForCurrentMonth: IListingAvailability[];
  rentalRates?: IListingRate[];
  rentalRatesForDate?: IListingRate;
  rentalDataForStartDate?: IListingRate;
  setPopUpInfo?: (_i: DatePickerInfoPopUp) => void;
  blockedStartDates: Date[];
  blockedEndDates: Date[];
};

const CheckoutDateItem = ({
  date,
  insideCurrentMonth,
  selected,
  dateObject,
  onSelectDate,
  availabilityData,
  availabilityDataForCurrentMonth,
  rentalRatesForDate,
  rentalRates = [],
  setPopUpInfo,
  rentalDataForStartDate,
  blockedStartDates,
  blockedEndDates,
}: Props) => {
  const dayIsForcedCheckinDay = useMemo(
    () => isForcedCheckinDay(dateObject, rentalRates),
    [dateObject, rentalRates],
  );

  const blockedRanges = useMemo(
    () => getBlockedRangesForDate(availabilityData, dateObject),
    [availabilityData, dateObject],
  );

  const isBlockStartDate = useMemo(
    () => blockedStartDates.some((_d) => isSameDay(_d, dateObject)),
    [blockedStartDates, dateObject],
  );

  const isBlockEndDate = useMemo(
    () => blockedEndDates.some((_d) => isSameDay(_d, dateObject)),
    [blockedEndDates, dateObject],
  );

  const isBeforeToday = useMemo(
    () => isBefore(startOfDay(dateObject), startOfDay(new Date())),
    [dateObject],
  );

  const isRangeValid = useMemo(
    () => !!(selected?.from && selected?.to && isValid(selected.from) && isValid(selected.to)),
    [selected?.from, selected?.to],
  );

  const isSelected = useMemo(
    () =>
      ((selected?.from && isSameDay(dateObject, selected?.from)) ||
        (selected?.to && isSameDay(dateObject, selected?.to))) &&
      insideCurrentMonth,
    [dateObject, insideCurrentMonth, selected?.from, selected?.to],
  );

  const isHighlighted = useMemo(() => {
    return (
      selected?.from &&
      isValid(selected.from) &&
      isAfter(dateObject, selected.from) &&
      selected?.to &&
      isValid(selected.to) &&
      isBefore(dateObject, selected.to) &&
      insideCurrentMonth
    );
  }, [dateObject, insideCurrentMonth, selected]);

  const checkinDay = useMemo(
    () => getCheckinDayRelativeToADate(rentalRates ?? [], dateObject, availabilityData),
    [availabilityData, dateObject, rentalRates],
  );

  const isStart = useMemo(
    () => selected?.from && isSameDay(dateObject, selected.from),
    [dateObject, selected],
  );
  const isEnd = useMemo(
    () => selected?.to && isSameDay(dateObject, selected.to),
    [dateObject, selected],
  );

  const isDisabled = useMemo(() => {
    const [firstCheckinDay, lastCheckinDay] = getStartAndEndForcedCheckinDay(rentalRates ?? []);
    return (
      checkIfDateDisabledDesktop(
        checkinDay,
        firstCheckinDay,
        lastCheckinDay,
        selected,
        rentalRatesForDate,
        availabilityData,
      ) ||
      (blockedRanges.length > 0 &&
        !selected?.from &&
        !blockedStartDates.some((_d) => isSameDay(startOfDay(dateObject), startOfDay(_d))) &&
        !blockedEndDates.some((_d) => isSameDay(startOfDay(dateObject), startOfDay(_d))))
    );
  }, [
    availabilityData,
    blockedEndDates,
    blockedRanges.length,
    blockedStartDates,
    checkinDay,
    dateObject,
    rentalRates,
    rentalRatesForDate,
    selected,
  ]);

  const onClick = useCallback(
    (e: MouseEvent<any>) => {
      if (isBeforeToday || isDisabled) {
        return;
      }
      if (selected?.from && isSameDay(dateObject, selected.from)) {
        onSelectDate(undefined);
        return;
      }

      const target = e.currentTarget;
      const boundingRect = target?.getBoundingClientRect();
      const pushLeft = window.innerWidth - Math.round(boundingRect?.right) < 150;

      const isCheckInRestricted = checkIfDateCannotBeCheckedIn(
        dateObject,
        checkinDay,
        rentalRatesForDate,
        rentalDataForStartDate,
      );

      if (checkinDay) {
        setPopUpInfo?.({
          left: Math.round(boundingRect?.left),
          bottom:
            window.innerHeight -
            Math.round(boundingRect?.bottom ?? 0) +
            Math.round((window.innerWidth - 32) / 7),
          right: pushLeft ? window.innerWidth - Math.round(boundingRect?.right) : undefined,
          text: `From ${checkinDay} to ${checkinDay}`,
        });
      } else {
        if (
          (selected?.from && !selected?.to && isCheckInRestricted) ||
          (selected?.from && isSameDay(selected.from, dateObject)) ||
          (!selected?.from && !isCheckInRestricted)
        ) {
          setPopUpInfo?.({
            left: Math.round(boundingRect?.left),
            bottom:
              window.innerHeight -
              Math.round(boundingRect?.bottom ?? 0) +
              Math.round((window.innerWidth - 32) / 7),
            right: pushLeft ? window.innerWidth - Math.round(boundingRect?.right) : undefined,
            text: `${
              !selected?.from
                ? rentalRatesForDate?.minimum_nights_stay
                : rentalDataForStartDate?.minimum_nights_stay
            }-night minimum`,
          });
        }
      }

      if (
        (!selected?.from && !rentalRatesForDate?.allow_checkin) ||
        (selected?.from && !rentalRatesForDate?.allow_checkout) ||
        isCheckInRestricted
      ) {
        return;
      }

      if (!selected?.from || isRangeValid) {
        onSelectDate({
          from: dateObject,
          to: undefined,
        });
        return;
      }

      if (selected?.from && isAfter(dateObject, selected.from)) {
        onSelectDate({
          from: selected.from,
          to: dateObject,
        });
      } else {
        onSelectDate({
          from: dateObject,
          to: undefined,
        });
        return;
      }
    },
    [
      checkinDay,
      dateObject,
      isBeforeToday,
      isDisabled,
      isRangeValid,
      onSelectDate,
      rentalDataForStartDate,
      rentalRatesForDate,
      selected,
      setPopUpInfo,
    ],
  );

  return (
    <td
      id={`date__item-${format(dateObject, 'yyyy-dd-MM')}`}
      className='relative z-1 group '
      onClick={onClick}
    >
      {insideCurrentMonth && (
        <>
          <div
            className={classNames(
              'text-sm flex flex-col items-center justify-center w-full h-full rounded-full aspect-square font-medium',
              {
                '!bg-carolina-blue': isSelected,
                'bg-carolina-blue-20': isHighlighted,
                'text-metal-gray': isBeforeToday || (isDisabled && !isSelected && !isHighlighted),
                'text-white': isStart || isEnd,
                'group-active:border group-active:border-solid group-active:border-foundation-blue':
                  !isDisabled && !isBeforeToday,
                __IsDisibled: isDisabled,
                '!font-bold': dayIsForcedCheckinDay && !isBeforeToday && !isDisabled,
                '!text-[#757575]': isBlockStartDate || isBlockEndDate,
              },
            )}
          >
            <p
              className={classNames('z-[2]', {
                'text-black': isHighlighted,
                'line-through': isBeforeToday || (isDisabled && !isSelected && !isHighlighted),
              })}
            >
              {date}
            </p>
          </div>
        </>
      )}
    </td>
  );
};

export default memo(CheckoutDateItem);
