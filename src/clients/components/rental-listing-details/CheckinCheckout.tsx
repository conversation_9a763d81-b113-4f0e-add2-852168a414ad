'use client';

import { memo, useCallback, useEffect, useState } from 'react';

import { ChevronDownIcon } from '@heroicons/react/24/outline';

import InputLabel from '@/app/ui/input-label';
import LoadingSpinner from '@/app/ui/loading-spinner';
import { useRequestToBook } from '@/clients/contexts/RequestToBookContext';
import { Popover, PopoverContent, PopoverTrigger } from '@/clients/ui/popover';
import { IListingDetails } from '@/types/rental-listing-details';

import { format } from 'date-fns';
import dynamic from 'next/dynamic';
import { Drawer } from 'vaul';

export const MOBILE_CHECKOUT_CALENDAR_WRAPPER = 'MOBILE_CHECKOUT_CALENDAR_WRAPPER';

const CheckoutDateRangePicker = dynamic(() => import('./CheckoutDateRangePicker/index'), {
  loading: () => (
    <div className='md:min-w-[570px] md:min-h-[468px] flex items-center justify-center'>
      <LoadingSpinner />
    </div>
  ),
});

const CheckoutDateRangePickerMobile = dynamic(
  () => import('./CheckoutDateRangePicker/CheckoutDateRangePickerMobile'),
  {
    loading: () => (
      <div className='fixed bg-black/40 inset-0 flex items-center justify-center text-white'>
        <LoadingSpinner className='w-10 h-10' />
      </div>
    ),
  },
);

type Props = {
  isMobile?: boolean;
  propertyDetails: IListingDetails;
  propertyId: number;
};

const CheckinCheckout = ({ propertyDetails, propertyId, isMobile }: Props) => {
  const { date, setDate, rentInfo } = useRequestToBook();
  const [open, setOpen] = useState<boolean>(false);
  const [mobileOpen, setMobileOpen] = useState<boolean>(false);

  const onClear = useCallback(() => {
    setDate(undefined);
  }, [setDate]);

  const onClose = useCallback(() => {
    setOpen(false);
  }, []);

  const onToggleMobileDatePicker = useCallback(() => {
    setMobileOpen(false);
  }, []);

  const onClickedTrigger = useCallback((e: any) => {
    e.preventDefault();
  }, []);

  const onClick = useCallback(() => {
    if (isMobile) {
      setMobileOpen(true);
    } else {
      setOpen(true);
    }
  }, [isMobile]);

  useEffect(() => {
    if (date?.from && date?.to) {
      onClose();
    }
  }, [date?.from, date?.to, onClose]);

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild onClick={onClickedTrigger}>
          <div className='flex items-center gap-x-2'>
            <div className='w-1/2'>
              <InputLabel className='px-[14px]'>CHECK IN</InputLabel>
              <div
                onClick={onClick}
                onKeyDown={onClick}
                role='button'
                tabIndex={0}
                className='border border-solid border-platinium flex items-center gap-x-2 py-2.5 px-3.5 text-sm  text-metal-gray rounded-[40px] relative cursor-pointer'
              >
                {date?.from ? format(date?.from, 'LLL d, yyyy') : 'Add Date'}
                <ChevronDownIcon className='w-4 h-4 text-foundation-blue absolute right-4' />
              </div>
            </div>
            <div className='w-1/2'>
              <InputLabel className='px-[14px]'>CHECK OUT</InputLabel>
              <div
                onClick={onClick}
                onKeyDown={onClick}
                role='button'
                tabIndex={0}
                className='border border-solid border-platinium flex items-center gap-x-2 py-2.5 px-3.5 text-sm  text-metal-gray rounded-[40px] relative cursor-pointer'
              >
                {date?.to ? format(date?.to, 'LLL d, yyyy') : 'Add Date'}
                <ChevronDownIcon className='w-4 h-4 text-foundation-blue absolute right-4' />
              </div>
            </div>
          </div>
        </PopoverTrigger>

        {open && (
          <PopoverContent
            className='md:min-w-[554px] w-max border border-solid border-english-manor border-opacity-40 p-6'
            align='end'
          >
            <CheckoutDateRangePicker
              date={date}
              setDate={setDate}
              onClear={onClear}
              onClose={onClose}
              rentalRates={propertyDetails.rates}
              availableCalendar={propertyDetails.availabilities}
              rentInfo={rentInfo}
              propertyId={propertyId}
            />
          </PopoverContent>
        )}
        {/* {mobileOpen && (
          <Drawer.Root direction='bottom' open={mobileOpen} onOpenChange={onToggleMobileDatePicker}>
            <Drawer.Portal>
              <Drawer.Overlay className='fixed inset-0 bg-black/40' />
              <Drawer.Content className='bg-gray-100 flex flex-col rounded-t-[10px] mt-24 fixed bottom-0 left-0 right-0 outline-none z-50'>
                <Drawer.Title className='hidden'></Drawer.Title>
                <Drawer.Description className='hidden'>Hidden description</Drawer.Description>
                {mobileOpen && (
                  <div id={MOBILE_CHECKOUT_CALENDAR_WRAPPER}>
                    <CheckoutDateRangePickerMobile
                      date={date}
                      setDate={setDate}
                      onClose={onToggleMobileDatePicker}
                      availableCalendar={propertyDetails.availabilities.sort(
                        (_a1, _a2) =>
                          new Date(_a1.from_date).getTime() - new Date(_a2.from_date).getTime(),
                      )}
                      rentalRates={propertyDetails.rates}
                    />
                  </div>
                )}
              </Drawer.Content>
            </Drawer.Portal>
          </Drawer.Root>
        )} */}
      </Popover>
    </>
  );
};

export default memo(CheckinCheckout);
