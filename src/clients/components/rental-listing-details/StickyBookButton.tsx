'use client';

import React, { useCallback, useContext, useMemo, useRef, useState } from 'react';

import LoadingSpinner from '@/app/ui/loading-spinner';
import { RentalDetailsContext } from '@/clients/contexts/RentalDetailsContext';
import { useRequestToBook } from '@/clients/contexts/RequestToBookContext';
import Button from '@/clients/ui/button';
import { IListingDetails } from '@/types/rental-listing-details';
import { currencyFormatterRound } from '@/utils/common';

import { differenceInCalendarDays } from 'date-fns';
import dynamic from 'next/dynamic';
import { Drawer } from 'vaul';

import { MOBILE_CHECKOUT_CALENDAR_WRAPPER } from './CheckinCheckout';

const CheckoutDateRangePickerMobile = dynamic(
  () => import('./CheckoutDateRangePicker/CheckoutDateRangePickerMobile'),
  {
    loading: () => (
      <div className='fixed bg-black/40 inset-0 flex items-center justify-center text-white'>
        <LoadingSpinner className='w-10 h-10' />
      </div>
    ),
  },
);

type Props = {
  details: IListingDetails;
  petsAllowed?: boolean;
};

const StickyBookButton = ({ details, petsAllowed }: Props) => {
  const [isFetchingBookingDetails, setIsFetchingBookingDetails] = useState<boolean>(false);
  const [openDatepicker, setOpenDatepicker] = useState<boolean>(false);
  const [openSummary, setOpenSummary] = useState<boolean>(false);
  const { date, setDate, guests, rentInfo, petCount, petType, petDescription, isPetSelected } =
    useRequestToBook();
  const dateRef = useRef(date);

  const onToggleDatePicker = useCallback(() => {
    setOpenDatepicker(!openDatepicker);
  }, [openDatepicker]);

  const onToggleSummaryDialog = useCallback(() => {
    setOpenSummary(!openSummary);
  }, [openSummary]);

  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date.to, date.from) : 0),
    [date?.from, date?.to],
  );
  const { setOpenContactAgentForm, setOpenBookingForm } = useContext(RentalDetailsContext);

  const onSubmit = useCallback(() => {
    if (rentInfo) {
      setOpenBookingForm(true);
    } else {
      onToggleDatePicker();
    }
  }, [onToggleDatePicker, rentInfo, setOpenBookingForm]);

  return (
    <>
      <div className='fixed bottom-0 left-0 right-0 z-[5] flex items-center justify-between gap-4 bg-white px-4 py-5 shadow-card md:hidden rounded-t-2xl'>
        <div onClick={onToggleSummaryDialog} role='presentation'>
          <p className='m-0 text-2xl font-medium'>
            {currencyFormatterRound.format(
              rentInfo?.rent ? (rentInfo?.rent / numberOfNights) * 7 : details?.peak_rate,
            )}
          </p>
          <span className='text-xs font-normal ml-2 !mb-3'>Per Week</span>
        </div>
        <Button
          onClick={onSubmit}
          title='Check Availability'
          className='rounded-[32px] py-3 px-9'
        />
        {rentInfo && (
          <p className='m-0 text-sm text-center font-medium mt-3 text-metal-gray'>
            You will not be charged yet.
          </p>
        )}
      </div>
      <Drawer.Root direction='bottom' open={openDatepicker} onOpenChange={onToggleDatePicker}>
        <Drawer.Portal>
          <Drawer.Overlay className='fixed inset-0 bg-black/40' />
          <Drawer.Content className='bg-gray-100 flex flex-col rounded-t-[10px] mt-24 fixed bottom-0 left-0 right-0 outline-none z-50'>
            <Drawer.Title className='hidden'></Drawer.Title>
            <Drawer.Description className='hidden'>Hidden description</Drawer.Description>
            {openDatepicker && (
              <div id={MOBILE_CHECKOUT_CALENDAR_WRAPPER}>
                <CheckoutDateRangePickerMobile
                  date={date}
                  setDate={setDate}
                  onClose={onToggleDatePicker}
                  availableCalendar={details.availabilities.sort(
                    (_a1, _a2) =>
                      new Date(_a1.from_date).getTime() - new Date(_a2.from_date).getTime(),
                  )}
                  rentInfo={rentInfo}
                  rentalRates={details.rates}
                  isFetchingBookingDetails={isFetchingBookingDetails}
                />
              </div>
            )}
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>
    </>
  );
};

export default StickyBookButton;
