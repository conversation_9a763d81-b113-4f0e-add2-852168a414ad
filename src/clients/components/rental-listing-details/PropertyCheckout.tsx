'use client';

import { useCallback, useContext, useMemo, useRef, useState } from 'react';

import { ChevronDownIcon } from '@heroicons/react/24/outline';

import FormHelperText from '@/app/ui/form-helper-text';
import InputLabel from '@/app/ui/input-label';
import { RentalDetailsContext } from '@/clients/contexts/RentalDetailsContext';
import { useRequestToBook } from '@/clients/contexts/RequestToBookContext';
import Button from '@/clients/ui/button';
import { SegmentEvents } from '@/types/analytics';
import { ProgressStatus } from '@/types/common';
import { IListingDetails } from '@/types/rental-listing-details';
import {
  formatAvailabilityDateString,
  getDatesBetweenDates,
  rangesOverlap,
} from '@/utils/availability';
import { checkIfDateHasRate, getRentalStartMonth, isBetweenTwoDates } from '@/utils/calendar';
import { currencyFormatterRound, getStringSingularPlural, Nullable } from '@/utils/common';

import classNames from 'classnames';
import { differenceInCalendarDays } from 'date-fns';
import dayjs from 'dayjs';
import dynamic from 'next/dynamic';

import { DateRange } from '../date-range-picker';
import { GuestsValues } from '../guest-selector';

import CheckinCheckout from './CheckinCheckout';

const RequestBookingForm = dynamic(() => import('./request-booking-from'), {
  ssr: false,
});
const ContactAgentForm = dynamic(() => import('./contact-agent-form'), {
  ssr: false,
});
const GuestSelectorComponent = dynamic(() => import('./GuestSelectorComponent'), {
  ssr: false,
});
const PricesAndSummary = dynamic(() => import('./PricesAndSummary'), {
  ssr: false,
});

type Props = {
  details: IListingDetails;
  isMobile?: boolean;
  petsAllowed?: boolean;
};

const PropertyCheckout = ({ details, isMobile, petsAllowed }: Props) => {
  const { guests, isPetSelected, petCount, rentInfo, date } = useRequestToBook();
  const [datePickerError, setDatePickerError] = useState<Nullable<string>>(null);
  const [open, setOpen] = useState<boolean>(false);
  const [isGuestsValid, setGuestValuesValid] = useState<boolean>(true);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | undefined>(undefined);

  const { openContactAgentForm, setOpenContactAgentForm, openBookingForm, setOpenBookingForm } =
    useContext(RentalDetailsContext);
  const availabilities = useMemo(() => details?.availabilities ?? [], [details?.availabilities]);
  const rentalRates = useMemo(() => details?.rates ?? [], [details?.rates]);
  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date.to, date.from) : 0),
    [date?.from, date?.to],
  );
  const [dateRange, setDateRange] = useState<DateRange>([getRentalStartMonth(), null]);

  const onToggle = useCallback(() => {
    setOpen((prev) => !prev);
  }, []);

  const onChangeRange = useCallback((range: DateRange) => {
    setDateRange(range);
  }, []);

  const onClickRequestBooking = useCallback(() => {
    // window?.analytics?.track(SegmentEvents.BOOKING_REQUEST_CLICKED, {
    //   action: 'CTA clicked',
    //   amenities: details.featured_amenities,
    //   capacity: details.capacity,
    //   checkin_date: dayjs(dateRange[0]).format('YYYY-MM-DD'),
    //   checkout_date: dayjs(dateRange[1]).format('YYYY-MM-DD'),
    //   listing_id: details.listing_id,
    //   listing_name: details.address,
    //   listing_number_of_bedrooms: details.bedroom_number,
    //   neighborhood: details.area_name,
    //   num_adults: guests.adults ?? 1,
    //   num_children: guests.children ?? 0,
    //   number_of_days: dayjs(dateRange[1]).diff(dateRange[0], 'days'),
    //   number_of_guests: Number(guests.adults ?? 1) + Number(guests.children ?? 0),
    //   price: details.peak_rate,
    //   region: 'Massachusetts',
    //   city: 'Nantucket',
    //   country: 'United States',
    //   url: document.URL,
    //   referrer: document.referrer,
    // });
    setOpenBookingForm(true);
  }, [setOpenBookingForm]);

  return (
    <>
      <div className='rounded-2xl rounded-b-none md:rounded-b-2xl border border-solid border-platinium shadow-card-25 p-6 bg-white'>
        <p className='m-0 text-[50px] font-medium'>
          {currencyFormatterRound.format(
            rentInfo?.rent ? (rentInfo?.rent / numberOfNights) * 7 : details?.peak_rate,
          )}
          <span className='text-base font-normal ml-2 !mb-3'>Per Week</span>
        </p>
        <div className='my-8'>
          <CheckinCheckout propertyDetails={details} propertyId={details.listing_id} />
          {datePickerError && (
            <FormHelperText className='pl-2.5' error>
              {datePickerError}
            </FormHelperText>
          )}

          <div className='mt-6'>
            <InputLabel className='px-[14px]'>GUESTS</InputLabel>
            <div
              className={classNames(
                'border border-solid border-platinium py-2.5 px-3.5 text-sm text-metal-gray rounded-[40px] transition-all h-[42px]',
                { '!rounded-2xl !h-auto !p-3.5': open },
              )}
            >
              <div
                className='relative cursor-pointer flex items-center'
                onClick={onToggle}
                role='presentation'
              >
                {getStringSingularPlural('Adult', 'Adults', guests.adults)},{' '}
                {getStringSingularPlural('Child', 'Children', guests.children)}
                {isPetSelected &&
                  petCount > 0 &&
                  `, ${getStringSingularPlural('Pet', 'Pets', petCount)}`}
                <ChevronDownIcon
                  className={classNames('w-4 h-4 text-foundation-blue absolute right-4', {
                    'rotate-180': open,
                  })}
                />
              </div>
              {open && (
                <GuestSelectorComponent petsAllowed={petsAllowed} capacity={details.capacity} />
              )}
            </div>
            <p className='m-0 mt-2 text-[10px] uppercase text-metal-gray px-3.5'>
              This property has a maximum of {details.capacity} guests. <br />
              {!petsAllowed
                ? `Pets are not allowed.`
                : `Pets allowed with prior permission, fees may apply.`}
            </p>
          </div>

          {rentInfo && <PricesAndSummary />}

          <Button
            title='Reserve'
            className='w-full mt-4 mb-2 rounded-[32px] py-4 font-medium'
            onClick={onClickRequestBooking}
            disabled={progressStatus === ProgressStatus.LOADING}
          />
          <p className='m-0 text-metal-gray py-2 text-center text-sm'>
            You will not be charged yet.
          </p>
        </div>
        {openBookingForm && (
          <RequestBookingForm
            details={details}
            open={openBookingForm}
            isMobile={isMobile}
            onClose={() => setOpenBookingForm(false)}
            dateRange={dateRange}
            adultsCount={guests?.adults}
            childrenCount={guests?.children}
          />
        )}
        {openContactAgentForm && (
          <ContactAgentForm
            open={openContactAgentForm}
            onClose={() => setOpenContactAgentForm(false)}
            details={details}
          />
        )}
      </div>
      <Button
        title='Ask A Question'
        className='w-full border-none hidden md:block text-olive mt-2'
        onClick={() => setOpenContactAgentForm(true)}
        intent='outline'
      />
    </>
  );
};

export default PropertyCheckout;
